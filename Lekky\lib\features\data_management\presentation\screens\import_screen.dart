// File: lib/features/data_management/presentation/screens/import_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

import '../../../../core/shared_modules/data_import_service.dart';
import '../../../../features/meter_readings/domain/models/meter_reading.dart';
import '../../../../features/top_ups/domain/models/top_up.dart';
import '../../data/csv_parser.dart';
import '../../domain/conflict_resolver.dart';
import '../../domain/import_validator.dart';

/// A screen for importing data from CSV files
class ImportScreen extends StatefulWidget {
  /// The file to import
  final File file;

  /// The history controller adapter
  final HistoryControllerAdapter historyController;

  /// Whether to replace existing data
  final bool replaceExistingData;

  /// Pre-parsed entries (optional - if provided, skips parsing step)
  final List<MeterEntry>? preParsedEntries;

  /// Callback when the import is complete
  final Function(List<MeterEntry>)? onComplete;

  /// Constructor
  const ImportScreen({
    Key? key,
    required this.file,
    required this.historyController,
    this.replaceExistingData = false,
    this.preParsedEntries,
    this.onComplete,
  }) : super(key: key);

  @override
  State<ImportScreen> createState() => _ImportScreenState();
}

class _ImportScreenState extends State<ImportScreen> {
  /// The current import step
  late ImportStep _currentStep;

  /// The progress of the current step (0.0 to 1.0)
  double _progress = 0.0;

  /// The status message
  String _statusMessage = 'Preparing to import...';

  /// Whether an error occurred
  bool _hasError = false;

  /// The error message
  String? _errorMessage;

  /// The parsed entries
  List<MeterEntry> _entries = [];

  /// The validation issues
  List<String> _validationIssues = [];

  /// The conflict resolver
  final _conflictResolver = ConflictResolver();

  /// The detected conflicts
  Map<MeterEntry, MeterEntry> _conflicts = {};

  /// The selected conflict resolution strategy
  ConflictStrategy _conflictStrategy = ConflictStrategy.skip;

  @override
  void initState() {
    super.initState();
    print('ImportScreen: initState called');
    _currentStep = ImportStep.parsing;
    _startImport();
  }

  /// Start the import process
  Future<void> _startImport() async {
    try {
      // Step 1: Parse the file
      await _parseFile();

      // Step 2: Validate the entries
      await _validateEntries();

      // Step 3: Check for conflicts
      await _checkConflicts();

      // If we have conflicts and we're not replacing all data, show conflict resolution
      if (_conflicts.isNotEmpty && !widget.replaceExistingData) {
        setState(() {
          _currentStep = ImportStep.resolveConflicts;
          _progress = 0.0;
          _statusMessage = 'Please resolve conflicts...';
        });
      } else {
        // Otherwise, proceed to import
        await _importEntries();
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Import failed: $e';
        _progress = 0.0;
      });
    }
  }

  /// Parse the CSV file
  Future<void> _parseFile() async {
    setState(() {
      _currentStep = ImportStep.parsing;
      _progress = 0.0;
      _statusMessage = 'Parsing CSV file...';
    });

    // Use pre-parsed entries if available, otherwise parse the file
    if (widget.preParsedEntries != null) {
      // Use pre-parsed entries (already parsed by DataImportService)
      setState(() {
        _entries = widget.preParsedEntries!;
        _progress = 1.0;
        _statusMessage = 'Found ${_entries.length} entries';
      });
    } else {
      // Simulate progress
      for (int i = 1; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        setState(() {
          _progress = i / 10;
        });
      }

      // Parse the file using CsvParser (fallback for direct ImportScreen usage)
      final parser = CsvParser();
      final result = await parser.parseFile(widget.file);

      if (result.isSuccess) {
        setState(() {
          _entries = result.value;
          _progress = 0.5;
          _statusMessage = 'Found ${_entries.length} entries';
        });
      } else {
        throw result.error.message;
      }

      // Simulate more progress
      for (int i = 6; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        setState(() {
          _progress = i / 10;
        });
      }
    }
  }

  /// Validate the entries
  Future<void> _validateEntries() async {
    setState(() {
      _currentStep = ImportStep.validating;
      _progress = 0.0;
      _statusMessage = 'Validating entries...';
    });

    // Simulate progress
    for (int i = 1; i <= 5; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      setState(() {
        _progress = i / 10;
      });
    }

    // Validate the entries
    final validator = ImportValidator();
    _validationIssues = await validator.validateEntries(_entries);

    setState(() {
      _progress = 0.5;
      if (_validationIssues.isEmpty) {
        _statusMessage = 'All entries are valid';
      } else {
        _statusMessage = 'Found ${_validationIssues.length} validation issues';
      }
    });

    // Simulate more progress
    for (int i = 6; i <= 10; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      setState(() {
        _progress = i / 10;
      });
    }
  }

  /// Check for conflicts with existing entries
  Future<void> _checkConflicts() async {
    // Skip if we're replacing all data
    if (widget.replaceExistingData) {
      return;
    }

    setState(() {
      _currentStep = ImportStep.checkingConflicts;
      _progress = 0.0;
      _statusMessage = 'Checking for conflicts...';
    });

    // Simulate progress
    for (int i = 1; i <= 3; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      setState(() {
        _progress = i / 10;
      });
    }

    // Get existing entries
    final allEntries = await widget.historyController.getAllEntries();

    // Convert to MeterEntry list
    final existingEntries = allEntries.map((entry) {
      if (entry is MeterReading) {
        // Check if this is a dismissed entry
        final typeCode = entry.status == EntryStatus.ignored ? 2 : 0;
        return MeterEntry.fromTypeCodeAndAmount(
          id: entry.id,
          typeCode: typeCode,
          amount: entry.value,
          timestamp: entry.date,
          notes: entry.notes,
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      } else if (entry is TopUp) {
        return MeterEntry.fromTypeCodeAndAmount(
          id: entry.id,
          typeCode: 1, // Top-up
          amount: entry.amount,
          timestamp: entry.date,
          notes: entry.notes,
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      } else {
        throw Exception('Unknown entry type');
      }
    }).toList();

    // Simulate more progress
    for (int i = 4; i <= 6; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      setState(() {
        _progress = i / 10;
      });
    }

    // Check for conflicts
    _conflicts = _conflictResolver.detectConflicts(_entries, existingEntries);

    setState(() {
      _progress = 0.7;
      if (_conflicts.isEmpty) {
        _statusMessage = 'No conflicts found';
      } else {
        _statusMessage = 'Found ${_conflicts.length} conflicts';
      }
    });

    // Simulate more progress
    for (int i = 7; i <= 10; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      setState(() {
        _progress = i / 10;
      });
    }
  }

  /// Import the entries
  Future<void> _importEntries() async {
    setState(() {
      _currentStep = ImportStep.importing;
      _progress = 0.0;
      _statusMessage = 'Importing entries...';
    });

    try {
      // If we have conflicts and we're not replacing all data, resolve them
      if (_conflicts.isNotEmpty && !widget.replaceExistingData) {
        // Get existing entries
        final allEntries = await widget.historyController.getAllEntries();

        // Convert to MeterEntry list
        final existingEntries = allEntries.map((entry) {
          if (entry is MeterReading) {
            // Check if this is a dismissed entry
            final typeCode = entry.status == EntryStatus.ignored ? 2 : 0;
            return MeterEntry.fromTypeCodeAndAmount(
              id: entry.id,
              typeCode: typeCode,
              amount: entry.value,
              timestamp: entry.date,
              notes: entry.notes,
              shortAverageAfterTopUp: null,
              totalAverageUpToThisPoint: null,
            );
          } else if (entry is TopUp) {
            return MeterEntry.fromTypeCodeAndAmount(
              id: entry.id,
              typeCode: 1, // Top-up
              amount: entry.amount,
              timestamp: entry.date,
              notes: entry.notes,
              shortAverageAfterTopUp: null,
              totalAverageUpToThisPoint: null,
            );
          } else {
            throw Exception('Unknown entry type');
          }
        }).toList();

        // Resolve conflicts
        final resolvedEntries = _conflictResolver.resolveConflicts(
          _conflicts,
          _entries,
          existingEntries,
          _conflictStrategy,
        );

        // Replace all entries
        final success = await widget.historyController.replaceAllEntries(
          resolvedEntries,
          onProgress: (progress) {
            setState(() {
              _progress = progress;
              _statusMessage =
                  'Importing entries (${(progress * 100).toInt()}%)...';
            });
          },
        );

        if (!success) {
          throw 'Failed to import entries';
        }
      } else {
        // Add entries to the database
        final success = await widget.historyController.bulkAddEntries(
          _entries,
          replace: widget.replaceExistingData,
          onProgress: (progress) {
            setState(() {
              _progress = progress;
              _statusMessage =
                  'Importing entries (${(progress * 100).toInt()}%)...';
            });
          },
        );

        if (!success) {
          throw 'Failed to import entries';
        }
      }

      // Show results
      setState(() {
        _currentStep = ImportStep.complete;
        _progress = 1.0;
        _statusMessage = 'Import complete';
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Import failed: $e';
        _progress = 0.0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Importing Data'),
        backgroundColor: AppColors.primary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProgressCard(),
            const SizedBox(height: 16),
            if (_hasError) _buildErrorCard(),
            if (_currentStep == ImportStep.resolveConflicts)
              _buildConflictResolutionCard(),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Build the progress card
  Widget _buildProgressCard() {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Import Progress',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 8),
          Text(
            _statusMessage,
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          _buildStepIndicator(),
        ],
      ),
    );
  }

  /// Build the step indicator
  Widget _buildStepIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildStepCircle(ImportStep.parsing, 'Parse'),
        _buildStepDivider(ImportStep.parsing, ImportStep.validating),
        _buildStepCircle(ImportStep.validating, 'Validate'),
        _buildStepDivider(ImportStep.validating, ImportStep.checkingConflicts),
        _buildStepCircle(ImportStep.checkingConflicts, 'Conflicts'),
        _buildStepDivider(ImportStep.checkingConflicts, ImportStep.importing),
        _buildStepCircle(ImportStep.importing, 'Import'),
      ],
    );
  }

  /// Build a step circle
  Widget _buildStepCircle(ImportStep step, String label) {
    final isActive = _currentStep.index >= step.index;
    final isComplete = _currentStep.index > step.index;

    return Column(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? AppColors.primary : Colors.grey[300],
          ),
          child: isComplete
              ? const Icon(Icons.check, color: Colors.white, size: 16)
              : null,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isActive ? AppColors.primary : Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Build a step divider
  Widget _buildStepDivider(ImportStep fromStep, ImportStep toStep) {
    final isActive = _currentStep.index >= toStep.index;

    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : Colors.grey[300],
      ),
    );
  }

  /// Build the error card
  Widget _buildErrorCard() {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 24),
              const SizedBox(width: 8),
              Text(
                'Error',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'An unknown error occurred',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  /// Build the conflict resolution card
  Widget _buildConflictResolutionCard() {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resolve Conflicts',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Found ${_conflicts.length} entries that conflict with existing data. How would you like to handle them?',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          _buildConflictStrategyOption(
            ConflictStrategy.skip,
            'Skip conflicting entries',
            'Keep existing entries and ignore conflicting new ones',
          ),
          _buildConflictStrategyOption(
            ConflictStrategy.replace,
            'Replace existing entries',
            'Use the new entries instead of the existing ones',
          ),
          _buildConflictStrategyOption(
            ConflictStrategy.keepBoth,
            'Keep both entries',
            'Keep both the existing and new entries',
          ),
          _buildConflictStrategyOption(
            ConflictStrategy.merge,
            'Merge entries',
            'Combine the data from existing and new entries',
          ),
        ],
      ),
    );
  }

  /// Build a conflict strategy option
  Widget _buildConflictStrategyOption(
      ConflictStrategy strategy, String title, String description) {
    return RadioListTile<ConflictStrategy>(
      title: Text(title,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
          )),
      subtitle: Text(description, style: AppTextStyles.bodySmall),
      value: strategy,
      groupValue: _conflictStrategy,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _conflictStrategy = value;
          });
        }
      },
    );
  }

  /// Build the action buttons
  Widget _buildActionButtons() {
    if (_hasError) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _hasError = false;
                _errorMessage = null;
              });
              _startImport();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      );
    } else if (_currentStep == ImportStep.resolveConflicts) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _importEntries();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Continue'),
          ),
        ],
      );
    } else {
      return Center(
        child: ElevatedButton(
          onPressed: _currentStep == ImportStep.complete
              ? () {
                  // Complete the import and call the callback
                  if (widget.onComplete != null) {
                    widget.onComplete!(_entries);
                  }

                  // Navigate back to the previous screen
                  Navigator.of(context).pop();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Done'),
        ),
      );
    }
  }
}

/// The import steps
enum ImportStep {
  /// Parsing the CSV file
  parsing,

  /// Validating the entries
  validating,

  /// Checking for conflicts
  checkingConflicts,

  /// Resolving conflicts
  resolveConflicts,

  /// Importing the entries
  importing,

  /// Import complete
  complete,
}
