import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'core/constants/app_constants.dart';
import 'core/di/service_locator.dart';
import 'core/localization/app_localizations.dart';

import 'core/providers/riverpod_localization_provider.dart';
import 'core/providers/settings_provider.dart';

import 'core/providers/theme_provider.dart' as theme_provider;
import 'core/shared/models/theme_mode.dart';

import 'core/theme/app_theme.dart';
import 'core/theme/app_colors.dart';
import 'features/splash/presentation/screens/simplified_splash_screen.dart';
import 'features/setup/presentation/screens/setup_screen.dart';
import 'features/welcome/presentation/screens/welcome_screen.dart';
import 'features/settings/presentation/screens/settings_screen.dart';
import 'features/reminders/presentation/widgets/reactive_reminder_listener.dart';
import 'features/notifications/presentation/widgets/reactive_alert_listener.dart';
import 'features/notifications/presentation/screens/notification_debug_screen.dart';
import 'features/settings/presentation/screens/csv_export_screen.dart';
import 'features/settings/presentation/screens/csv_import_screen.dart';
import 'features/settings/presentation/screens/about_screen.dart';
import 'features/settings/presentation/screens/donate_screen.dart';
import 'features/settings/presentation/screens/appearance_screen.dart';
import 'features/settings/presentation/screens/region_screen.dart';
import 'features/settings/presentation/screens/language_screen.dart';
import 'features/settings/presentation/screens/currency_screen.dart';
import 'features/settings/presentation/screens/date_screen.dart';
// TODO: Migrate these screens to Riverpod
// import 'features/settings/presentation/screens/notifications_screen.dart';
import 'features/settings/presentation/screens/update_screen.dart';
import 'features/settings/presentation/screens/alert_threshold_screen.dart';
import 'features/settings/presentation/screens/days_advance_screen.dart';
import 'features/settings/presentation/screens/notification_types_screen.dart';
import 'features/settings/presentation/screens/reminders_screen.dart';
import 'features/settings/presentation/screens/notification_utilities_screen.dart';
import 'features/settings/presentation/screens/tips_tricks_screen.dart';
import 'features/settings/presentation/screens/delete_all_data_screen.dart';
import 'features/settings/presentation/screens/date_format_screen.dart';
import 'features/settings/presentation/screens/time_display_screen.dart';
import 'features/settings/presentation/screens/theme_mode_screen.dart';

import 'features/home/<USER>/screens/riverpod_dashboard_screen.dart';
import 'features/history/presentation/screens/riverpod_history_screen.dart';

import 'features/notifications/data/notification_service.dart';
import 'features/validation/presentation/screens/riverpod_validation_dashboard_screen.dart';
import 'features/cost/presentation/screens/cost_screen.dart';
import 'core/services/background_monitoring_service.dart';
import 'core/services/unified_alert_manager.dart';
import 'core/services/post_migration_validation_service.dart';
import 'core/services/status_field_migration_service.dart';
import 'features/validation/domain/services/dismissed_entry_migration.dart';
import 'core/constants/currency_constants.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize timezone data
  tz.initializeTimeZones();

  // Initialize the service locator
  setupServiceLocator();

  // Run comprehensive status field synchronization (includes dismissed entry fixes)
  try {
    await StatusFieldMigrationService.runStatusFieldSynchronization();
    debugPrint('Status field synchronization completed');
  } catch (e) {
    debugPrint('Failed to run status field synchronization: $e');
    // Continue app initialization even if migration fails
  }

  // Run dismissed entry migration if needed
  try {
    await DismissedEntryMigration.runMigrationIfNeeded();
    debugPrint('Dismissed entry migration completed');
  } catch (e) {
    debugPrint('Failed to run dismissed entry migration: $e');
    // Continue app initialization even if migration fails
  }

  // Run post-migration validation to ensure existing entries are properly validated
  try {
    await PostMigrationValidationService.checkAndRunPostMigrationValidation();
    debugPrint('Post-migration validation completed');
  } catch (e) {
    debugPrint('Failed to run post-migration validation: $e');
    // Continue app initialization even if validation fails
  }

  // Theme management now handled by Riverpod providers

  // Initialize the notification service with proper setup
  try {
    final notificationService =
        await serviceLocator.getAsync<NotificationService>();
    final bool initialized = await notificationService.initialize();
    if (initialized) {
      debugPrint('Notification service initialized successfully');
    } else {
      debugPrint(
          'Notification service initialization failed - notifications will not work');
    }
  } catch (e) {
    debugPrint('Failed to initialize notification service: $e');
    // Continue app initialization even if notifications fail
  }

  // Initialize background monitoring service
  try {
    final backgroundService = BackgroundMonitoringService();
    await backgroundService.initialize();
    await backgroundService.updateMonitoring();
    debugPrint('Background monitoring service initialized');
  } catch (e) {
    debugPrint('Failed to initialize background monitoring: $e');
    // Continue app initialization even if background monitoring fails
  }

  // Reminder scheduling now handled by reactive Riverpod system
  debugPrint('Reminder scheduling handled by reactive Riverpod system');

  // Initialize unified alert manager
  try {
    final alertManager = UnifiedAlertManager();
    await alertManager.checkAndFireAlerts();
    debugPrint('Unified alert manager initialized');
  } catch (e) {
    debugPrint('Failed to initialize alert manager: $e');
    // Continue app initialization even if alert manager fails
  }

  // Localization now handled by Riverpod providers

  runApp(const LekkyApp());
}

class LekkyApp extends StatelessWidget {
  const LekkyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: Consumer(
        builder: (context, ref, _) {
          final localizationAsync = ref.watch(riverpodLocalizationProvider);
          final themeAsync = ref.watch(theme_provider.themeProvider);

          return localizationAsync.when(
            data: (localizationState) => ReactiveAlertListener(
              child: ReactiveNotificationListener(
                child: MaterialApp(
                  title: 'Lekky',
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: themeAsync.when(
                    data: (themeState) =>
                        themeState.themeMode == AppThemeMode.light
                            ? ThemeMode.light
                            : themeState.themeMode == AppThemeMode.dark
                                ? ThemeMode.dark
                                : ThemeMode.system,
                    loading: () => ThemeMode.system,
                    error: (_, __) => ThemeMode.system,
                  ),
                  debugShowCheckedModeBanner: false,
                  locale: Locale(localizationState.languageCode),
                  supportedLocales: RegionalConstants.supportedLanguageCodes
                      .map((code) => Locale(code))
                      .toList(),
                  localizationsDelegates: const [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  initialRoute: AppConstants.routeSplash,
                  routes: {
                    AppConstants.routeSplash: (context) =>
                        const SimplifiedSplashScreen(),
                    AppConstants.routeWelcome: (context) =>
                        const WelcomeScreen(),
                    AppConstants.routeSetup: (context) => const SetupScreen(),
                    AppConstants.routeHome: (context) => const MainScreen(),
                    '/history': (context) =>
                        const MainScreen(initialTabIndex: 1),
                    '/main-settings': (context) =>
                        const MainScreen(initialTabIndex: 3),
                    '/settings': (context) => const SettingsScreen(),
                    '/settings/csv/export': (context) =>
                        const CsvExportScreen(),
                    '/settings/csv/import': (context) =>
                        const CsvImportScreen(),
                    '/settings/data/delete-all': (context) =>
                        const DeleteAllDataScreen(),
                    '/settings/about': (context) => const AboutScreen(),
                    '/settings/donate': (context) => const DonateScreen(),
                    '/settings/donate/options': (context) =>
                        const DonateScreen(),
                    // Migrated screens
                    '/settings/appearance': (context) =>
                        const AppearanceScreen(),
                    '/settings/region': (context) => const RegionScreen(),
                    '/settings/region/language': (context) =>
                        const LanguageScreen(),
                    '/settings/region/currency': (context) =>
                        const CurrencyScreen(),
                    '/settings/date': (context) => const DateScreen(),
                    // '/settings/notifications': (context) =>
                    //     const NotificationsScreen(),
                    '/settings/about/info': (context) => const AboutScreen(),
                    '/settings/about/update': (context) => const UpdateScreen(),
                    '/settings/about/tips': (context) =>
                        const TipsTricksScreen(),
                    '/settings/notifications/threshold': (context) =>
                        const AlertThresholdScreen(),
                    '/settings/notifications/days': (context) =>
                        const DaysAdvanceScreen(),
                    '/settings/notifications/types': (context) =>
                        const NotificationTypesScreen(),
                    '/settings/notifications/reminders': (context) =>
                        const RemindersScreen(),
                    '/settings/notifications/utilities': (context) =>
                        const NotificationUtilitiesScreen(),
                    '/settings/date/format': (context) =>
                        const DateFormatScreen(),
                    '/settings/date/time': (context) =>
                        const TimeDisplayScreen(),
                    '/settings/appearance/theme': (context) =>
                        const ThemeModeScreen(),
                    AppConstants.routeValidationDashboard: (context) =>
                        const RiverpodValidationDashboardScreen(),
                    '/debug/notifications': (context) =>
                        const NotificationDebugScreen(),
                  },
                ),
              ),
            ),
            loading: () => const MaterialApp(
              home: Scaffold(
                body: Center(child: CircularProgressIndicator()),
              ),
            ),
            error: (_, __) => const MaterialApp(
              home: Scaffold(
                body: Center(child: Text('Error loading localization')),
              ),
            ),
          );
        },
      ),
    );
  }
}

// WelcomeScreen is now implemented in features/welcome/presentation/screens/welcome_screen.dart

class MainScreen extends StatefulWidget {
  /// The initial tab index to show
  final int initialTabIndex;

  /// Constructor
  const MainScreen({super.key, this.initialTabIndex = 0});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late int _selectedIndex;

  static const List<Widget> _screens = [
    DashboardTab(),
    HistoryTab(),
    CostTab(),
    SettingsTab(),
  ];

  late Future<void> _initNotificationsFuture;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialTabIndex;

    // Initialize the notification controller
    _initNotificationsFuture = _initializeNotifications();
  }

  /// Initialize notifications - migrated to Riverpod
  Future<void> _initializeNotifications() async {
    try {
      // Notification initialization now handled by Riverpod providers
      debugPrint('Notification initialization handled by Riverpod providers');
    } catch (e) {
      debugPrint('Unexpected error in notification initialization: $e');
      // Continue app initialization even if notifications fail
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  /// Get selected navigation color based on current screen and theme
  Color _getSelectedNavColor(int selectedIndex, bool isDark) {
    switch (selectedIndex) {
      case 0: // Dashboard
        return isDark ? AppColors.homeAppBarDark : AppColors.homeAppBarLight;
      case 1: // History
        return isDark
            ? AppColors.historyAppBarDark
            : AppColors.historyAppBarLight;
      case 2: // Cost
        return isDark ? AppColors.costAppBarDark : AppColors.costAppBarLight;
      case 3: // Settings
        return isDark
            ? AppColors.settingsAppBarDark
            : AppColors.settingsAppBarLight;
      default:
        return isDark ? AppColors.primaryDark : AppColors.primary;
    }
  }

  /// Switch to the History tab
  void switchToHistoryTab() {
    setState(() {
      _selectedIndex = 1; // Index of the History tab
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initNotificationsFuture,
      builder: (context, snapshot) {
        return Scaffold(
          // Remove the AppBar as we'll create a custom header in each screen
          body: _screens[_selectedIndex],
          bottomNavigationBar: Consumer(
            builder: (context, ref, _) {
              final settingsAsync = ref.watch(settingsProvider);

              return settingsAsync.when(
                loading: () => BottomNavigationBar(
                  items: const <BottomNavigationBarItem>[
                    BottomNavigationBarItem(
                      icon: Icon(Icons.home),
                      label: 'Home',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.history),
                      label: 'History',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.attach_money),
                      label: 'Cost',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.settings),
                      label: 'Settings',
                    ),
                  ],
                  currentIndex: _selectedIndex,
                  onTap: _onItemTapped,
                  type: BottomNavigationBarType.fixed,
                ),
                error: (error, stackTrace) => BottomNavigationBar(
                  items: const <BottomNavigationBarItem>[
                    BottomNavigationBarItem(
                      icon: Icon(Icons.home),
                      label: 'Home',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.history),
                      label: 'History',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.attach_money),
                      label: 'Cost',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.settings),
                      label: 'Settings',
                    ),
                  ],
                  currentIndex: _selectedIndex,
                  onTap: _onItemTapped,
                  type: BottomNavigationBarType.fixed,
                ),
                data: (settings) {
                  // Get currency-specific icon for Cost tab
                  final costIcon =
                      RegionalConstants.getCurrencyIcon(settings.currency);

                  final isDark =
                      Theme.of(context).brightness == Brightness.dark;

                  return BottomNavigationBar(
                    items: <BottomNavigationBarItem>[
                      const BottomNavigationBarItem(
                        icon: Icon(Icons.home),
                        label: 'Home',
                      ),
                      const BottomNavigationBarItem(
                        icon: Icon(Icons.history),
                        label: 'History',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(costIcon),
                        label: 'Cost',
                      ),
                      const BottomNavigationBarItem(
                        icon: Icon(Icons.settings),
                        label: 'Settings',
                      ),
                    ],
                    currentIndex: _selectedIndex,
                    selectedItemColor:
                        _getSelectedNavColor(_selectedIndex, isDark),
                    unselectedItemColor: Theme.of(context)
                        .bottomNavigationBarTheme
                        .unselectedItemColor,
                    onTap: _onItemTapped,
                    type: BottomNavigationBarType.fixed,
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

// Dashboard tab for the main screen
class DashboardTab extends StatelessWidget {
  const DashboardTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const RiverpodDashboardScreen();
  }
}

class HistoryTab extends StatelessWidget {
  const HistoryTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const RiverpodHistoryScreen();
  }
}

class CostTab extends StatelessWidget {
  const CostTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const CostScreen();
  }
}

class SettingsTab extends StatelessWidget {
  const SettingsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const SettingsScreen();
  }
}
