import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/dismissed_entry_detector.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../domain/services/data_integrity_service.dart';

/// One-time migration service for dismissed entries
class DismissedEntryMigration {
  static const String _migrationKey =
      PreferenceKeys.dismissedEntryMigrationCompleted;

  /// Run migration if not already completed
  static Future<void> runMigrationIfNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isCompleted = prefs.getBool(_migrationKey) ?? false;

      if (isCompleted) {
        Logger.info(
            'DismissedEntryMigration: Migration already completed, skipping');
        return;
      }

      Logger.info(
          'DismissedEntryMigration: Starting migration of dismissed entries');
      await _performMigration();

      // Mark migration as completed
      await prefs.setBool(_migrationKey, true);
      Logger.info(
          'DismissedEntryMigration: Migration completed and marked as done');

      // Trigger full validation recalculation
      await _triggerValidationRecalculation();
    } catch (e) {
      Logger.error('DismissedEntryMigration: Migration failed: $e');
      // Don't mark as completed if migration failed
    }
  }

  /// Perform the actual migration
  static Future<void> _performMigration() async {
    final meterReadingRepo = serviceLocator<MeterReadingRepository>();
    final allReadings = await meterReadingRepo.getAllMeterReadings();

    // Find all dismissed entries
    final dismissedEntries = allReadings.where(_isDismissedEntry).toList();

    if (dismissedEntries.isEmpty) {
      Logger.info(
          'DismissedEntryMigration: No dismissed entries found to migrate');
      return;
    }

    Logger.info(
        'DismissedEntryMigration: Found ${dismissedEntries.length} dismissed entries to migrate');

    // Group dismissed entries by gap periods
    final dismissedGroups =
        await _groupDismissedEntriesByGap(dismissedEntries, allReadings);

    Logger.info(
        'DismissedEntryMigration: Found ${dismissedGroups.length} dismissed groups');

    int migratedCount = 0;
    for (final group in dismissedGroups) {
      final migrated = await _migrateDismissedGroup(group, allReadings);
      if (migrated) {
        migratedCount++;
      }
    }

    Logger.info(
        'DismissedEntryMigration: Successfully migrated $migratedCount dismissed groups');
  }

  /// Check if an entry is a dismissed entry
  static bool _isDismissedEntry(MeterReading reading) {
    return DismissedEntryDetector.isDismissedEntry(reading);
  }

  /// Group dismissed entries by their gap periods
  static Future<List<List<MeterReading>>> _groupDismissedEntriesByGap(
    List<MeterReading> dismissedEntries,
    List<MeterReading> allReadings,
  ) async {
    final groups = <List<MeterReading>>[];
    final processedEntries = <int>{};

    // Get non-dismissed entries for gap detection
    final realEntries = allReadings
        .where((reading) => !_isDismissedEntry(reading))
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));

    for (final dismissedEntry in dismissedEntries) {
      if (processedEntries.contains(dismissedEntry.id)) continue;

      // Find the gap this dismissal entry belongs to
      final gapInfo = _findGapForDismissedEntry(dismissedEntry, realEntries);
      if (gapInfo == null) continue;

      // Find all dismissed entries in this gap
      final groupEntries = dismissedEntries
          .where((entry) =>
              !processedEntries.contains(entry.id) &&
              entry.date.isAfter(gapInfo.start) &&
              entry.date.isBefore(gapInfo.end))
          .toList();

      if (groupEntries.isNotEmpty) {
        groups.add(groupEntries);
        for (final entry in groupEntries) {
          processedEntries.add(entry.id!);
        }
      }
    }

    return groups;
  }

  /// Find the gap that a dismissed entry belongs to
  static _GapInfo? _findGapForDismissedEntry(
    MeterReading dismissedEntry,
    List<MeterReading> realEntries,
  ) {
    for (int i = 1; i < realEntries.length; i++) {
      final start = realEntries[i - 1].date;
      final end = realEntries[i].date;

      if (dismissedEntry.date.isAfter(start) &&
          dismissedEntry.date.isBefore(end)) {
        return _GapInfo(start: start, end: end);
      }
    }
    return null;
  }

  /// Migrate a group of dismissed entries to a single entry
  static Future<bool> _migrateDismissedGroup(
    List<MeterReading> groupEntries,
    List<MeterReading> allReadings,
  ) async {
    if (groupEntries.isEmpty) return false;

    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();

      // Find the gap for this group
      final realEntries = allReadings
          .where((reading) => !_isDismissedEntry(reading))
          .toList()
        ..sort((a, b) => a.date.compareTo(b.date));

      final firstEntry = groupEntries.first;
      final gapInfo = _findGapForDismissedEntry(firstEntry, realEntries);

      if (gapInfo == null) {
        Logger.warning(
            'DismissedEntryMigration: Could not find gap for dismissed entry group');
        return false;
      }

      // Calculate new dismissal date (start + 62 days)
      final newDismissalDate = gapInfo.start.add(const Duration(days: 62));

      // Check if new date would be valid (before end date)
      if (newDismissalDate.isAfter(gapInfo.end) ||
          newDismissalDate.isAtSameMomentAs(gapInfo.end)) {
        Logger.warning(
            'DismissedEntryMigration: Gap too short for new dismissal entry');
        // Delete all entries in this group since gap is too short
        for (final entry in groupEntries) {
          if (entry.id != null) {
            await meterReadingRepo.deleteMeterReading(entry.id!);
          }
        }
        return true;
      }

      // Create new single dismissal entry
      final gapDays = gapInfo.end.difference(gapInfo.start).inDays;
      final newDismissalEntry = MeterReading(
        value: 0.0,
        date: newDismissalDate,
        status: EntryStatus.ignored,
        notes:
            'Dismissed missing entry gap: $gapDays days (${_formatDate(gapInfo.start)} - ${_formatDate(gapInfo.end)})',
      );

      // Add new entry
      await meterReadingRepo.addMeterReading(newDismissalEntry);

      // Delete old entries
      for (final entry in groupEntries) {
        if (entry.id != null) {
          await meterReadingRepo.deleteMeterReading(entry.id!);
        }
      }

      Logger.info(
          'DismissedEntryMigration: Migrated ${groupEntries.length} entries to 1 entry for gap ${gapInfo.start} - ${gapInfo.end}');
      return true;
    } catch (e) {
      Logger.error('DismissedEntryMigration: Error migrating group: $e');
      return false;
    }
  }

  /// Trigger full validation recalculation after migration
  static Future<void> _triggerValidationRecalculation() async {
    try {
      Logger.info(
          'DismissedEntryMigration: Triggering validation recalculation');
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      await dataIntegrityService.validateAndUpdateAllMeterReadings();
      await dataIntegrityService.validateAndUpdateAllTopUps();
      Logger.info(
          'DismissedEntryMigration: Validation recalculation completed');
    } catch (e) {
      Logger.error(
          'DismissedEntryMigration: Error during validation recalculation: $e');
    }
  }

  /// Format date for display
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Helper class for gap information
class _GapInfo {
  final DateTime start;
  final DateTime end;

  _GapInfo({required this.start, required this.end});
}
