import '../utils/database_inspector.dart';
import '../utils/logger.dart';

/// Service for debugging database issues during development
class DatabaseDebugService {
  /// Investigate Entry ID 214 specifically (the dismissed entry from screenshots)
  static Future<void> investigateEntry214() async {
    Logger.info('DatabaseDebugService: Starting investigation of Entry ID 214');
    await DatabaseInspector.investigateEntry(214, isMeterReading: true);
  }

  /// Investigate all dismissed entries in the database
  static Future<void> investigateAllDismissedEntries() async {
    try {
      Logger.info('DatabaseDebugService: Starting investigation of all dismissed entries');
      
      final dbInfo = await DatabaseInspector.getDetailedDatabaseInfo();
      final validationStatus = dbInfo['validation_status'];
      
      if (validationStatus != null) {
        final ignoredCount = validationStatus['meter_readings']['ignored'] as int;
        Logger.info('DatabaseDebugService: Found $ignoredCount dismissed meter readings');
        
        if (ignoredCount > 0) {
          // Log general database state
          await DatabaseInspector.logDatabaseContents();
        }
      }
    } catch (e) {
      Logger.error('DatabaseDebugService: Error investigating dismissed entries: $e');
    }
  }

  /// Run comprehensive database investigation
  static Future<void> runFullInvestigation() async {
    Logger.info('DatabaseDebugService: Starting full database investigation');
    
    // Investigate Entry ID 214 specifically
    await investigateEntry214();
    
    // Investigate all dismissed entries
    await investigateAllDismissedEntries();
    
    Logger.info('DatabaseDebugService: Full investigation completed');
  }
}
