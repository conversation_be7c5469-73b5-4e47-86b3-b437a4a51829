// File: lib/features/validation/domain/services/new_dismissed_entry_service.dart
import '../../../../core/utils/logger.dart';
import '../../../../core/di/service_locator.dart';
import '../models/validation_issue.dart';
import '../repositories/dismissed_gap_repository.dart';
import 'simple_gap_detection_service.dart';

/// Simple service for managing dismissed gaps (no more fake entries)
class DismissedEntryService {
  final SimpleGapDetectionService _gapDetectionService;

  const DismissedEntryService(this._gapDetectionService);

  /// Dismiss a missing entry gap
  Future<void> createDismissalEntry(ValidationIssue issue) async {
    try {
      Logger.info('DismissedEntryService: Dismissing gap using new simple system');
      await _gapDetectionService.dismissGap(issue);
      Logger.info('DismissedEntryService: Gap dismissed successfully');
    } catch (e) {
      Logger.error('DismissedEntryService: Error dismissing gap: $e');
      rethrow;
    }
  }

  /// Check if a gap period has been dismissed (legacy method for compatibility)
  Future<bool> isGapDismissed(
      List<dynamic> readings, DateTime start, DateTime end) async {
    try {
      // Use the new gap detection service to check if gap is dismissed
      final dismissedGapRepository = serviceLocator<DismissedGapRepository>();
      return await dismissedGapRepository.isGapDismissed(start, end);
    } catch (e) {
      Logger.error('DismissedEntryService: Error checking if gap is dismissed: $e');
      return false;
    }
  }

  /// Check if an entry is a dismissed entry (legacy method - always returns false now)
  bool isDismissedEntry(dynamic reading) {
    // No more dismissed entries in the database - they're stored as gap metadata
    return false;
  }

  /// Clean up invalid dismissal entries (legacy method - does nothing now)
  Future<void> cleanupInvalidDismissalEntries() async {
    Logger.info('DismissedEntryService: No cleanup needed with new system');
    // The migration already cleaned up old dismissed entries
  }
}
